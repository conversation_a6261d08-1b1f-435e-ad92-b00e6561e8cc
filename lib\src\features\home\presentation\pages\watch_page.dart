// lib/src/features/home/<USER>/pages/watch_page.dart

import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:go_router/go_router.dart';
import 'package:xinglian/src/core/theme/app_colors.dart';
import 'package:xinglian/src/features/auth/bloc/auth_bloc.dart';
import 'package:xinglian/src/features/chat/repository/chat_repository.dart';
import 'package:xinglian/src/features/discovery/models/agent_model.dart';
import 'package:xinglian/src/features/home/<USER>/recommendation_bloc.dart';
import 'package:xinglian/src/features/home/<USER>/recommendation_models.dart';
import 'package:xinglian/src/features/home/<USER>/recommendation_repository.dart';
import 'package:xinglian/src/features/interactive_story/models/interactive_story_model.dart';

class WatchPage extends StatelessWidget {
  const WatchPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => RecommendationBloc(
        context.read<RecommendationRepository>(),
      )..add(LoadInitialRecommendations()),
      child: Scaffold(
        backgroundColor: AppColors.background,
        body: BlocBuilder<RecommendationBloc, RecommendationState>(
          builder: (context, state) {
            if (state is RecommendationLoading || state is RecommendationInitial) {
              return const Center(child: CircularProgressIndicator(color: AppColors.accentPurple));
            }
            if (state is RecommendationError) {
              return Center(child: Text(state.message, style: const TextStyle(color: Colors.red)));
            }
            if (state is RecommendationLoaded) {
              final agentItems = state.feedItems.where((item) => item.type == RecommendationItemType.agent).toList();
              final featuredAgent = agentItems.isNotEmpty ? agentItems.first : null;
              final gridItems = state.feedItems.where((item) => item != featuredAgent).toList();

              return CustomScrollView(
                slivers: [
                  _buildSliverAppBar(context),
                  SliverList(
                    delegate: SliverChildListDelegate(
                      [
                        const _BannerSection(),
                        _buildFeaturedSection(context, featuredAgent),
                        // [✓ 修复] 移除了 "为你推荐" 标题
                      ],
                    ),
                  ),
                  _buildRecommendationGrid(gridItems, context),
                ],
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }
}

// SliverAppBar 保持不变
Widget _buildSliverAppBar(BuildContext context) {
  // ... (代码保持不变)
  return SliverAppBar(
    backgroundColor: AppColors.background,
    elevation: 0,
    pinned: true,
    titleSpacing: 0,
    title: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => context.push('/search'),
              child: Container(
                height: 36,
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  color: AppColors.inputBackground.withOpacity(0.8),
                  borderRadius: BorderRadius.circular(18),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.search, color: AppColors.secondaryText, size: 20),
                    SizedBox(width: 8),
                    Text('角色/邀请码/兑换码', style: TextStyle(color: AppColors.secondaryText, fontSize: 14)),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.inputBackground.withOpacity(0.8),
              borderRadius: BorderRadius.circular(18),
            ),
            child: const Row(
              children: [
                Icon(Icons.flash_on, color: AppColors.accentYellow, size: 16),
                SizedBox(width: 4),
                Text('免费能量', style: TextStyle(color: Colors.white, fontSize: 12)),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

// Banner 区域保持不变
class _BannerSection extends StatelessWidget {
  const _BannerSection();
  @override
  Widget build(BuildContext context) {
    // ... (代码保持不变)
    final banner = {
        'image': 'https://i.imgur.com/your_banner_image_1.jpeg', // 请替换为您自己的图片URL
        'title': '卡显影UP池',
        'subtitle': '拾光之约周边上线 全服10抽免费送!',
      };

    return Container(
      height: 150,
      margin: const EdgeInsets.all(16),
      child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              image: DecorationImage(
                image: NetworkImage(banner['image']!),
                fit: BoxFit.cover,
                onError: (exception, stackTrace) => {},
              ),
            ),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  colors: [ Colors.black.withOpacity(0.0), Colors.black.withOpacity(0.6) ],
                  begin: Alignment.center,
                  end: Alignment.bottomCenter,
                ),
              ),
              padding: const EdgeInsets.all(16),
              alignment: Alignment.bottomLeft,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text( banner['title']!, style: const TextStyle( color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold, shadows: [Shadow(blurRadius: 4, color: Colors.black54)])),
                  Text( banner['subtitle']!, style: const TextStyle( color: Colors.white, fontSize: 12, shadows: [Shadow(blurRadius: 4, color: Colors.black54)])),
                ],
              ),
            ),
          )
    );
  }
}

// [✓ 修复] 承载左侧功能按钮列和右侧推荐卡片的二列布局
Widget _buildFeaturedSection(BuildContext context, RecommendationItem? featuredAgent) {
  return Padding(
    padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 24.0), // 调整了垂直边距
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 左侧功能按钮列
        Expanded(
          flex: 1, // [✓ 修复] 将flex设置为1，实现等宽
          child: _buildFunctionColumn(context),
        ),
        const SizedBox(width: 8), // [✓ 修复] 减小了中间的间距
        // 右侧推荐卡片
        Expanded(
          flex: 1, // [✓ 修复] 将flex设置为1，实现等宽
          child: featuredAgent != null
              ? _buildRecommendationCard(featuredAgent, context)
              : const SizedBox(),
        ),
      ],
    ),
  );
}

// 构建左侧垂直的功能按钮列表
Widget _buildFunctionColumn(BuildContext context) {
    // ... (代码保持不变)
  return Column(
    children: [
      _buildVerticalFunctionCard(context, '宿命回响', '多个TA同时回应你', Icons.people_alt, AppColors.accentPurple),
      const SizedBox(height: 8),
      _buildVerticalFunctionCard(context, '印象匹配', '千种印象任你选', Icons.favorite, Colors.pinkAccent),
      const SizedBox(height: 8),
      _buildVerticalFunctionCard(context, '自由创建', '自定义你的专属恋人', Icons.create, AppColors.accentBlue),
      const SizedBox(height: 8),
      _buildVerticalFunctionCard(context, '每日限免', '0点刷新', Icons.access_time, AppColors.accentOrange),
    ],
  );
}

// 构建单个垂直功能卡片的样式
Widget _buildVerticalFunctionCard(BuildContext context, String title, String subtitle, IconData icon, Color color) {
    // ... (代码保持不变)
  return GestureDetector(
    onTap: () {
      if (title == '自由创建') {
        context.push('/create-agent');
      }
    },
    child: ClipRRect(
      borderRadius: BorderRadius.circular(16.0),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.secondaryBg.withOpacity(0.5),
            borderRadius: BorderRadius.circular(16.0),
            border: Border.all(color: Colors.white.withOpacity(0.1)),
          ),
          child: Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: color.withOpacity(0.2),
                child: Icon(icon, color: color, size: 18),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(title, style: const TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.w500)),
                    Text(subtitle, style: const TextStyle(color: AppColors.secondaryText, fontSize: 10)),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

// [✓ 修复] 删除了 _buildRecommendationsTitle 方法
// Widget _buildRecommendationsTitle() { ... }

// 瀑布流网格的构建逻辑保持不变
Widget _buildRecommendationGrid(List<RecommendationItem> items, BuildContext context) {
  // ... (代码保持不变)
  if (items.isEmpty) {
    return const SliverToBoxAdapter(
      child: Center(
        child: Padding(
          padding: EdgeInsets.all(32.0),
          child: Text('暂无推荐内容', style: TextStyle(color: AppColors.secondaryText)),
        ),
      ),
    );
  }
  return SliverPadding(
    padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
    sliver: SliverMasonryGrid.count(
      crossAxisCount: 2,
      mainAxisSpacing: 12,
      crossAxisSpacing: 12,
      childCount: items.length,
      itemBuilder: (context, index) {
        return _buildRecommendationCard(items[index], context);
      },
    ),
  );
}

// 推荐卡片的样式和逻辑保持不变
Widget _buildRecommendationCard(RecommendationItem item, BuildContext context) {
  // ... (代码保持不变)
  String title = '未知';
  String? coverUrl;
  List<String> tags = [];
  String popularity = '0';
  String description = '...';

  if (item.type == RecommendationItemType.agent) {
    final agent = item.data as Agent;
    title = agent.name;
    coverUrl = agent.imageUrl;
    tags = agent.tags;
    popularity = _formatCount(agent.dialogueCount);
    description = agent.openingLine ?? agent.description;
  } else {
    final story = item.data as InteractiveStoryCard;
    title = story.title;
    coverUrl = story.coverUrl;
    popularity = _formatCount(story.viewCount);
    description = story.description ?? '...';
  }

  return InkWell(
    onTap: () async {
      final authState = context.read<AuthBloc>().state;
      if (authState is! Authenticated) return;
      
      final chatRepo = context.read<ChatRepository>();
      String? chatId;
      if (item.type == RecommendationItemType.agent) {
        chatId = await chatRepo.startAgentChat((item.data as Agent).id, authState.userId);
      } else {
        chatId = await chatRepo.startStory((item.data as InteractiveStoryCard).id, authState.userId);
      }

      if (chatId != null && context.mounted) {
        context.push('/chat/$chatId');
      }
    },
    borderRadius: BorderRadius.circular(12),
    child: ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Stack(
        children: [
          Image.network(
            coverUrl ?? '',
            fit: BoxFit.cover,
            errorBuilder: (_, __, ___) => Container(
              height: 250,
              color: AppColors.secondaryBg,
              child: const Icon(Icons.broken_image, color: AppColors.tertiaryText),
            ),
          ),
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.transparent, Colors.black.withOpacity(0.95)],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: const [0.6, 1.0],
                ),
              ),
            ),
          ),
          Positioned(
            bottom: 12,
            left: 12,
            right: 12,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (tags.isNotEmpty)
                  Wrap(
                    spacing: 4,
                    runSpacing: 4,
                    children: tags.take(2).map((tag) => Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(tag, style: const TextStyle(color: Colors.white, fontSize: 10)),
                    )).toList(),
                  ),
                if (tags.isNotEmpty) const SizedBox(height: 6),
                Text(
                  title,
                  style: const TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(color: AppColors.secondaryText, fontSize: 12),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 6),
                Row(
                  children: [
                    const Icon(Icons.chat_bubble_outline, color: AppColors.secondaryText, size: 12),
                    const SizedBox(width: 4),
                    Text(popularity, style: const TextStyle(color: AppColors.secondaryText, fontSize: 12)),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

// 格式化数字的辅助函数保持不变
String _formatCount(int count) {
  if (count >= 10000) {
    return '${(count / 10000).toStringAsFixed(1)}万';
  }
  return count.toString();
}
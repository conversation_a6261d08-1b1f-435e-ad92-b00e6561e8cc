import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:xinglian/src/features/chat/bloc/chat_player/chat_player_bloc.dart';
import 'package:xinglian/src/core/networking/api_client.dart';
import 'package:xinglian/src/core/theme/app_colors.dart';

class AgentChatAppBar extends StatefulWidget implements PreferredSizeWidget {
  final ChatPlayerState state;
  const AgentChatAppBar({super.key, required this.state});

  @override
  State<AgentChatAppBar> createState() => _AgentChatAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(120); // 增加高度以容纳更多信息
}

class _AgentChatAppBarState extends State<AgentChatAppBar> {
  Map<String, dynamic>? _memoryStatus;
  bool _isLoadingMemoryStatus = false;

  @override
  void initState() {
    super.initState();
    _loadMemoryStatus();
  }

  Future<void> _loadMemoryStatus() async {
    if (_isLoadingMemoryStatus) return;

    setState(() {
      _isLoadingMemoryStatus = true;
    });

    try {
      final apiClient = context.read<ApiClient>();
      final response = await apiClient.dio.get('/api/chats/${widget.state.chatId}/memory-status');

      if (response.statusCode == 200) {
        setState(() {
          _memoryStatus = response.data;
        });
      }
    } catch (e) {
      print('获取记忆状态失败: $e');
    } finally {
      setState(() {
        _isLoadingMemoryStatus = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final agent = widget.state.participants.isNotEmpty ? widget.state.participants.first : null;

    return SafeArea(
      bottom: false,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black.withOpacity(0.8),
              Colors.black.withOpacity(0.4),
            ],
          ),
        ),
        child: Column(
          children: [
            // 第一行：返回按钮、角色名称、快捷按钮
            Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
                  onPressed: () => context.pop(),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    agent?.name ?? '聊天',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                _buildQuickActionButton(
                  icon: Icons.favorite_outline,
                  label: '羁绊值',
                  onTap: () {
                    // TODO: 显示羁绊值详情
                  },
                ),
                const SizedBox(width: 8),
                _buildQuickActionButton(
                  icon: Icons.book_outlined,
                  label: '日记',
                  onTap: () {
                    // TODO: 跳转到日记页面
                  },
                ),
                const SizedBox(width: 8),
                _buildQuickActionButton(
                  icon: Icons.calendar_today_outlined,
                  label: '约会',
                  onTap: () {
                    // TODO: 跳转到约会页面
                  },
                ),
              ],
            ),
            const SizedBox(height: 8),
            // 第二行：等级、羁绊值、好感度信息
            Row(
              children: [
                const SizedBox(width: 48), // 对齐返回按钮的位置
                Expanded(
                  child: Row(
                    children: [
                      _buildStatusChip(
                        icon: Icons.star,
                        label: 'Lv.8',
                        color: AppColors.accentYellow,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildBondProgress(),
                      ),
                      const SizedBox(width: 12),
                      _buildStatusChip(
                        icon: Icons.favorite,
                        label: '85%',
                        color: AppColors.accentPurple,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 构建快捷操作按钮
  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.white.withOpacity(0.3)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.white, size: 16),
            const SizedBox(height: 2),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建状态芯片
  Widget _buildStatusChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.5)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 12),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  // 构建羁绊值进度条
  Widget _buildBondProgress() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '羁绊值',
          style: TextStyle(
            color: Colors.white,
            fontSize: 10,
          ),
        ),
        const SizedBox(height: 2),
        Container(
          height: 4,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(2),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: 0.75, // 75% 进度
            child: Container(
              decoration: BoxDecoration(
                color: AppColors.accentPurple,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
        ),
        const SizedBox(height: 2),
        const Text(
          '750/1000',
          style: TextStyle(
            color: Colors.white70,
            fontSize: 8,
          ),
        ),
      ],
    );
  }

  Widget _buildMemoryStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Future<void> _triggerSummaryUpdate() async {
    try {
      final apiClient = context.read<ApiClient>();
      await apiClient.dio.post('/api/chats/${widget.state.chatId}/summary/update');

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('摘要更新已触发'),
          backgroundColor: Colors.green,
        ),
      );

      // 重新加载记忆状态
      _loadMemoryStatus();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('摘要更新失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

}

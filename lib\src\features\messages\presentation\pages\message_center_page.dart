// 文件路径: lib/src/features/messages/presentation/pages/message_center_page.dart
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../bloc/message_center_bloc.dart';
import '../../../auth/bloc/auth_bloc.dart';
import '../../models/chat_list_item.dart';
import '../../repository/message_repository.dart';

class MessageCenterPage extends StatelessWidget {
  const MessageCenterPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => MessageCenterBloc(
        repository: RepositoryProvider.of<MessageRepository>(context),
        authBloc: BlocProvider.of<AuthBloc>(context),
      )..add(LoadChatList()),
      child: Scaffold(
        backgroundColor: AppColors.background,
        body: Stack(
          children: [
            ListView(
              padding: const EdgeInsets.only(bottom: 100),
              children: [
                _buildCustomHeader(context),
                _buildEventNotificationsSection(),
                _buildFriendDiscoverySection(),
                _buildMessagesListSection(),
              ],
            ),
            _buildBottomBanner(),
          ],
        ),
      ),
    );
  }
}

// 自定义页面顶部
Widget _buildCustomHeader(BuildContext context) {
  return SafeArea(
    bottom: false,
    child: Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  const Icon(Icons.auto_awesome, color: Colors.white, size: 28),
                  const SizedBox(width: 8),
                  Text('聊天', style: AppTextStyles.headline1.copyWith(fontSize: 28)),
                ],
              ),
              Row(
                children: [
                  IconButton(icon: const Icon(Icons.people_alt_outlined, color: AppColors.primaryText), onPressed: () {}),
                  IconButton(icon: const Icon(Icons.more_horiz, color: AppColors.primaryText), onPressed: () {}),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Text('事件通知', style: AppTextStyles.headline2.copyWith(fontSize: 16)),
                  const SizedBox(width: 4),
                  const Text('(3)', style: TextStyle(color: AppColors.secondaryText, fontSize: 16)),
                ],
              ),
              TextButton(
                onPressed: () {},
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text('全部事件', style: TextStyle(color: AppColors.secondaryText, fontSize: 14)),
                    Icon(Icons.arrow_forward_ios, size: 12, color: AppColors.secondaryText),
                  ],
                ),
              )
            ],
          ),
        ],
      ),
    ),
  );
}

// 事件通知模块
Widget _buildEventNotificationsSection() {
  final notifications = [
    {'title': '容身之处', 'subtitle': '初次见面，继兄将我堵在门口……', 'icon': Icons.home_work_outlined, 'color': AppColors.accentBlue, 'tag': '新关系'},
    {'title': '抢亲', 'subtitle': '把你从花轿上抢过来，你肯定恨极了我吧。', 'icon': Icons.favorite_border, 'color': AppColors.accentPurple, 'tag': '新事件'},
    {'title': '私藏心事', 'subtitle': '一个眼神的交汇，足以让他所有的伪装瞬间瓦解。', 'icon': Icons.lock_outline, 'color': AppColors.accentYellow, 'tag': '新事件'},
  ];

  return Container(
    height: 105,
    margin: const EdgeInsets.only(left: 16, top: 8),
    child: ListView.builder(
      scrollDirection: Axis.horizontal,
      itemCount: notifications.length,
      itemBuilder: (context, index) {
        final item = notifications[index];
        return _buildEventCard(
          title: item['title'] as String,
          subtitle: item['subtitle'] as String,
          icon: item['icon'] as IconData,
          color: item['color'] as Color,
          tag: item['tag'] as String,
        );
      },
    ),
  );
}

// 事件通知卡片样式
Widget _buildEventCard({required String title, required String subtitle, required IconData icon, required Color color, required String tag}) {
  return Container(
    width: 200,
    margin: const EdgeInsets.only(right: 12),
    decoration: BoxDecoration(
      color: AppColors.secondaryBg,
      borderRadius: BorderRadius.circular(16.0),
      gradient: RadialGradient(
        center: Alignment.topLeft,
        radius: 1.5,
        colors: [color.withOpacity(0.2), Colors.transparent],
      ),
      border: Border.all(color: AppColors.secondaryBg.withOpacity(0.5)),
    ),
    child: Stack(
      children: [
        Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(color: color.withOpacity(0.2), borderRadius: BorderRadius.circular(8)),
                    child: Row(
                      children: [
                        Icon(icon, color: color, size: 12),
                        const SizedBox(width: 4),
                        Text(tag, style: TextStyle(color: color, fontSize: 10, fontWeight: FontWeight.bold)),
                      ],
                    ),
                  ),
                ],
              ),
              const Spacer(),
              Text(title, style: const TextStyle(color: AppColors.primaryText, fontWeight: FontWeight.bold, fontSize: 14)),
              const SizedBox(height: 4),
              Text(subtitle, style: const TextStyle(color: AppColors.secondaryText, fontSize: 12), maxLines: 2, overflow: TextOverflow.ellipsis),
            ],
          ),
        ),
        Positioned(
          top: 8,
          right: 8,
          child: InkWell(onTap: () {}, child: const Icon(Icons.close, color: AppColors.secondaryText, size: 16)),
        ),
      ],
    ),
  );
}

// 发现好友模块
Widget _buildFriendDiscoverySection() {
  final friendUpdates = [{'name': '谢疏白', 'avatar': 'https://i.imgur.com/example_avatar1.png', 'tag': '限免'}];
  return Padding(
    padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('好友消息', style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold)),
        const SizedBox(height: 12),
        // [✓ 修复] 将SizedBox的高度从80增加到90，以解决溢出问题
        SizedBox(
          height: 90,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: friendUpdates.length + 1,
            itemBuilder: (context, index) {
              if (index == 0) return _buildAddFriendButton();
              final friend = friendUpdates[index - 1];
              return _buildFriendDiscoveryItem(friend);
            },
          ),
        ),
      ],
    ),
  );
}

Widget _buildAddFriendButton() {
  return Container(
    width: 60,
    margin: const EdgeInsets.only(right: 16),
    child: Column(
      children: [
        Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(shape: BoxShape.circle, color: AppColors.secondaryBg, border: Border.all(color: AppColors.tertiaryText, width: 1)),
          child: const Icon(Icons.add, color: AppColors.primaryText, size: 28),
        ),
        const SizedBox(height: 8), // 增加一点间距
        const Text('发现好友', style: TextStyle(color: AppColors.primaryText, fontSize: 12)),
      ],
    ),
  );
}

Widget _buildFriendDiscoveryItem(Map<String, String> friend) {
  return Container(
    width: 60,
    margin: const EdgeInsets.only(right: 16),
    child: Column(
      children: [
        Stack(
          clipBehavior: Clip.none,
          children: [
            CircleAvatar(radius: 28, backgroundImage: NetworkImage(friend['avatar']!)),
            Positioned(
              top: -2,
              right: -4,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                decoration: BoxDecoration(color: AppColors.accentPurple, borderRadius: BorderRadius.circular(8)),
                child: Text(friend['tag']!, style: const TextStyle(color: Colors.white, fontSize: 8, fontWeight: FontWeight.bold)),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8), // 增加一点间距
        Text(friend['name']!, style: const TextStyle(color: AppColors.primaryText, fontSize: 12), overflow: TextOverflow.ellipsis),
      ],
    ),
  );
}

// 消息列表主体
Widget _buildMessagesListSection() {
  return BlocBuilder<MessageCenterBloc, MessageCenterState>(
    builder: (context, state) {
      if (state is MessageCenterLoading) return const Center(child: CircularProgressIndicator(color: AppColors.accentPurple));
      if (state is MessageCenterLoaded) {
        if (state.chatList.isEmpty) return const Center(child: Padding(padding: EdgeInsets.all(32.0), child: Text('暂无消息', style: TextStyle(color: AppColors.secondaryText))));
        return Column(children: [
          const Padding(padding: EdgeInsets.symmetric(horizontal: 16.0), child: Divider(color: AppColors.secondaryBg, height: 1)),
          for (var chat in state.chatList) _buildChatListItem(context, chat)
        ]);
      }
      if (state is MessageCenterError) return Center(child: Text(state.message, style: const TextStyle(color: Colors.red)));
      return const SizedBox.shrink();
    },
  );
}

// 单个聊天项
Widget _buildChatListItem(BuildContext context, ChatListItem chat) {
  return InkWell(
    onTap: () => context.push('/chat/${chat.chatId}'),
    child: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      child: Row(
        children: [
          CircleAvatar(
            radius: 28,
            backgroundImage: (chat.displayAvatar != null && chat.displayAvatar!.isNotEmpty) ? NetworkImage(chat.displayAvatar!) : null,
            child: (chat.displayAvatar == null || chat.displayAvatar!.isEmpty) ? const Icon(Icons.person) : null,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(chat.displayName, style: AppTextStyles.body.copyWith(fontSize: 16, fontWeight: FontWeight.w500)),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(color: AppColors.accentPurple.withOpacity(0.3), borderRadius: BorderRadius.circular(4)),
                      child: Text('Lv.${chat.level}', style: const TextStyle(color: AppColors.accentPurple, fontSize: 10, fontWeight: FontWeight.bold)),
                    ),
                    if (chat.eventTag != null) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(color: AppColors.accentBlue.withOpacity(0.3), borderRadius: BorderRadius.circular(4)),
                        child: Text(chat.eventTag!, style: const TextStyle(color: AppColors.accentBlue, fontSize: 10, fontWeight: FontWeight.bold)),
                      ),
                    ]
                  ],
                ),
                const SizedBox(height: 6),
                Text(chat.latestMessage ?? '...', style: AppTextStyles.bodySecondary.copyWith(fontSize: 14), maxLines: 1, overflow: TextOverflow.ellipsis),
              ],
            ),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              if (chat.latestMessageTime != null) Text(_formatTimestamp(chat.latestMessageTime!), style: AppTextStyles.caption.copyWith(fontSize: 12)),
              const SizedBox(height: 8),
              if (chat.unreadCount > 0)
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: const BoxDecoration(color: Colors.red, shape: BoxShape.circle),
                  child: Text(chat.unreadCount.toString(), style: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold)),
                ),
            ],
          ),
        ],
      ),
    ),
  );
}

// 底部浮动横幅
Widget _buildBottomBanner() {
  return Positioned(
    bottom: 16,
    left: 16,
    right: 16,
    child: ClipRRect(
      borderRadius: BorderRadius.circular(25),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.6),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(color: Colors.white.withOpacity(0.2)),
          ),
          child: Row(
            children: [
              const SizedBox(width: 8),
              const Icon(Icons.cloud_outlined, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              const Expanded(child: Text('是否要接收TA的吃醋、冷落消息~', style: TextStyle(color: Colors.white, fontSize: 12))),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(color: Colors.red, borderRadius: BorderRadius.circular(10)),
                child: const Text('100+', style: TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold)),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.accentPurple,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  minimumSize: const Size(0, 32),
                ),
                child: const Text('接受', style: TextStyle(color: Colors.white, fontSize: 12)),
              ),
              IconButton(onPressed: () {}, icon: const Icon(Icons.close, color: Colors.white, size: 20)),
            ],
          ),
        ),
      ),
    ),
  );
}

// 时间格式化工具方法
String _formatTimestamp(String isoString) {
  try {
    final now = DateTime.now();
    final time = DateTime.parse(isoString).toLocal();
    if (now.year == time.year && now.month == time.month && now.day == time.day) {
      return DateFormat('HH:mm').format(time);
    } else if (now.year == time.year && now.month == time.month && now.day == time.day + 1) {
      return '昨天';
    } else {
      return DateFormat('MM/dd').format(time);
    }
  } catch (e) {
    return isoString.substring(5, 10);
  }
}
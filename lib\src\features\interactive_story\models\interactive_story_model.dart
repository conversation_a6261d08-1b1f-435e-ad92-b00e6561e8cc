// lib/src/features/interactive_story/models/interactive_story_model.dart
import 'package:equatable/equatable.dart';

class InteractiveStory extends Equatable {
  final String id;
  final String? coverUrl;
  final String title;
  final int popularity;
  final String? description;

  const InteractiveStory({
    required this.id,
    this.coverUrl,
    required this.title,
    required this.popularity,
    this.description,
  });

  factory InteractiveStory.fromJson(Map<String, dynamic> json) {
    return InteractiveStory(
      id: json['id'] as String,
      // 修复：后端返回的是 cover_image_url，不是 cover_url
      coverUrl: json['cover_image_url'] as String? ?? json['cover_url'] as String?,
      title: json['title'] as String? ?? '未命名故事',
      popularity: json['popularity'] as int? ?? 0,
      description: json['theme_prompt'] as String?,
    );
  }

  @override
  List<Object?> get props => [id, popularity];
}

// 专门用于推荐系统的故事卡片模型
class InteractiveStoryCard extends Equatable {
  final String id;
  final String? coverUrl;
  final String title;
  final String author;
  final int viewCount;
  final String? description;

  const InteractiveStoryCard({
    required this.id,
    this.coverUrl,
    required this.title,
    required this.author,
    required this.viewCount,
    this.description,
  });

  factory InteractiveStoryCard.fromJson(Map<String, dynamic> json) {
    return InteractiveStoryCard(
      id: json['id'] as String,
      coverUrl: json['cover_image_url'] as String? ?? json['cover_url'] as String?,
      title: json['title'] as String? ?? '未命名故事',
      // [✓ 修复] 直接提供一个默认值，因为 /api/stories 接口不包含作者信息。
      // 这样可以防止解析时因字段不存在而抛出异常。
      author: '匿名创作者',
      viewCount: json['popularity'] as int? ?? 0,
      description: json['theme_prompt'] as String?,
    );
  }

  @override
  List<Object?> get props => [id];
}

import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/app_colors.dart';

class FriendsPage extends StatefulWidget {
  const FriendsPage({super.key});

  @override
  State<FriendsPage> createState() => _FriendsPageState();
}

class _FriendsPageState extends State<FriendsPage> {
  // _collapsedSections 已被 ExpansionTile 替代

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        automaticallyImplyLeading: false,
        title: const Text(
          '羁绊',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.primaryText,
          ),
        ),
        actions: [
          TextButton.icon(
            onPressed: () {
              // TODO: 跳转到心伴小窗
            },
            icon: const Icon(Icons.add_circle_outline, color: AppColors.primaryText),
            label: const Text(
              '心伴小窗',
              style: TextStyle(color: AppColors.primaryText, fontSize: 14),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildTopFunctionCards(),
            const SizedBox(height: 16),
            _buildRelationshipSection(
              title: '初见',
              levelRequired: 1,
              characters: _getInitialCharacters(),
            ),
            _buildRelationshipSection(
              title: '恋人',
              levelRequired: 10,
              characters: _getLoverCharacters(),
            ),
            _buildRelationshipSection(
              title: '朋友',
              levelRequired: 5,
              characters: _getFriendCharacters(),
            ),
            _buildRelationshipSection(
              title: '家人',
              levelRequired: 15,
              characters: _getFamilyCharacters(),
            ),
            const SizedBox(height: 100), // 底部留白
          ],
        ),
      ),
    );
  }

  Widget _buildTopFunctionCards() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 左侧大卡片
          Expanded(
            flex: 2, // 占据更多空间
            child: _buildFunctionCard(
              title: '拾光之约',
              subtitle: '末显影 那一夜',
              icon: Icons.photo_library,
              color: AppColors.accentYellow,
              isLarge: true, // 新增参数
              onTap: () {},
            ),
          ),
          const SizedBox(width: 12),
          // 右侧两个小卡片
          Expanded(
            flex: 1,
            child: Column(
              children: [
                _buildFunctionCard(
                  title: '留影室',
                  subtitle: '留下美好瞬间',
                  icon: Icons.camera_alt,
                  color: AppColors.accentBlue,
                  onTap: () {},
                ),
                const SizedBox(height: 12),
                _buildFunctionCard(
                  title: '次元诊疗',
                  subtitle: 'TA确诊了肌肤饥渴症',
                  icon: Icons.healing,
                  color: AppColors.accentPurple,
                  onTap: () {},
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 修改 _buildFunctionCard 方法以支持不同尺寸和样式
  Widget _buildFunctionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    bool isLarge = false,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: AspectRatio(
        aspectRatio: isLarge ? 1.0 : 1.0, // 根据大小调整宽高比
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16.0),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.secondaryBg.withOpacity(0.5),
                borderRadius: BorderRadius.circular(16.0),
                border: Border.all(color: Colors.white.withOpacity(0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 图标和标题
                  Row(
                    children: [
                      Icon(icon, color: Colors.white, size: isLarge ? 24 : 20),
                      const SizedBox(width: 8),
                      Text(title, style: TextStyle(color: AppColors.primaryText, fontSize: isLarge ? 16 : 14, fontWeight: FontWeight.bold)),
                    ],
                  ),
                  // 副标题
                  Text(subtitle, style: TextStyle(color: AppColors.secondaryText, fontSize: isLarge ? 12 : 10)),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRelationshipSection({
    required String title,
    required int levelRequired,
    required List<Map<String, dynamic>> characters,
  }) {
    // 注意：这里我们不再使用 _collapsedSections 状态，ExpansionTile自带状态管理
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.secondaryBg,
        borderRadius: BorderRadius.circular(12),
      ),
      child: ExpansionTile(
        // 默认展开 "初见" 分组
        initiallyExpanded: title == '初见',
        title: Row(
          children: [
            Text(title, style: const TextStyle(color: AppColors.primaryText, fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(width: 8),
            Text('羁绊值Lv.$levelRequired', style: const TextStyle(color: AppColors.secondaryText, fontSize: 12)),
          ],
        ),
        iconColor: AppColors.secondaryText,
        collapsedIconColor: AppColors.secondaryText,
        children: [
          const Divider(color: AppColors.inputBackground, height: 1),
          Padding(
            padding: const EdgeInsets.all(16),
            // 这里使用 Column + for 循环，而不是 GridView，以避免嵌套滚动问题
            child: Column(
              children: [
                for (var char in characters) ...[
                  _buildCharacterCard(char),
                  const SizedBox(height: 12),
                ]
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCharacterCard(Map<String, dynamic> character) {
    final bondProgress = (character['bondProgress'] as int).toDouble();
    final maxBond = (character['maxBond'] as int? ?? 100).toDouble(); // 假设最大值为100
    final unlockProgress = bondProgress / maxBond;

    return InkWell(
      onTap: () {
        if (character['isUnlocked']) {
          context.push('/chat/${character['chatId']}');
        }
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        height: 120, // 固定高度
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppColors.inputBackground,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            // 左侧立绘
            AspectRatio(
              aspectRatio: 1.0,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(character['avatar'], fit: BoxFit.cover),
              ),
            ),
            const SizedBox(width: 12),
            // 右侧信息区
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 名称和等级
                  Row(
                    children: [
                      Text(character['name'], style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
                      const SizedBox(width: 6),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                        decoration: BoxDecoration(
                          color: AppColors.accentPurple.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text('Lv.${character['level']}', style: const TextStyle(color: AppColors.accentPurple, fontSize: 10)),
                      )
                    ],
                  ),
                  // 羁绊值进度条
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('羁绊值 ${bondProgress.toInt()}/${maxBond.toInt()}', style: const TextStyle(color: AppColors.secondaryText, fontSize: 10)),
                      const SizedBox(height: 4),
                      LinearProgressIndicator(
                        value: unlockProgress,
                        backgroundColor: Colors.white.withOpacity(0.2),
                        valueColor: const AlwaysStoppedAnimation<Color>(AppColors.accentPurple),
                      ),
                    ],
                  ),
                  // 解锁状态和操作按钮
                  Row(
                    children: [
                      Icon(Icons.lock_open, size: 12, color: AppColors.secondaryText),
                      const SizedBox(width: 4),
                      Text(character['unlockDescription'] ?? '已解锁', style: const TextStyle(color: AppColors.secondaryText, fontSize: 10)),
                      const Spacer(),
                      Container(
                        height: 28,
                        decoration: BoxDecoration(
                          gradient: AppColors.primaryGradient,
                          borderRadius: BorderRadius.circular(14),
                        ),
                        child: ElevatedButton.icon(
                          onPressed: () {},
                          icon: const Icon(Icons.chat_bubble, size: 12, color: Colors.white),
                          label: const Text('邀请TA', style: TextStyle(fontSize: 10, color: Colors.white)),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.transparent,
                            shadowColor: Colors.transparent,
                          ),
                        ),
                      )
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 模拟数据
  List<Map<String, dynamic>> _getInitialCharacters() {
    return [
      {
        'name': '小雪',
        'avatar': 'https://i.imgur.com/avatar1.png',
        'level': 3,
        'bondProgress': 45,
        'maxBond': 100,
        'isUnlocked': true,
        'chatId': 'chat_xiaoxue',
        'unlockDescription': '已解锁日记',
      },
      {
        'name': '小雨',
        'avatar': 'https://i.imgur.com/avatar2.png',
        'level': 1,
        'bondProgress': 20,
        'maxBond': 100,
        'isUnlocked': true,
        'chatId': 'chat_xiaoyu',
        'unlockDescription': '已解锁聊天',
      },
    ];
  }

  List<Map<String, dynamic>> _getLoverCharacters() {
    return [
      {
        'name': '心上人',
        'avatar': 'https://i.imgur.com/lover1.png',
        'level': 12,
        'bondProgress': 85,
        'isUnlocked': false,
        'chatId': 'chat_lover1',
      },
    ];
  }

  List<Map<String, dynamic>> _getFriendCharacters() {
    return [
      {
        'name': '好朋友',
        'avatar': 'https://i.imgur.com/friend1.png',
        'level': 7,
        'bondProgress': 60,
        'isUnlocked': false,
        'chatId': 'chat_friend1',
      },
    ];
  }

  List<Map<String, dynamic>> _getFamilyCharacters() {
    return [
      {
        'name': '家人',
        'avatar': 'https://i.imgur.com/family1.png',
        'level': 18,
        'bondProgress': 95,
        'isUnlocked': false,
        'chatId': 'chat_family1',
      },
    ];
  }
}

// _CircularProgressPainter 已被移除，因为新设计不再使用环形进度条

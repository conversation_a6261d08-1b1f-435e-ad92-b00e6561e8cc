import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:xinglian/src/features/auth/presentation/pages/login_page.dart';
import 'package:xinglian/src/features/auth/presentation/pages/register_page.dart';
import 'package:xinglian/src/features/chat/presentation/pages/chat_player_page.dart';
import 'package:xinglian/src/features/creation/presentation/pages/create_agent_page.dart';
import 'package:xinglian/src/features/creation/presentation/pages/create_story_page.dart';
import 'package:xinglian/src/features/creation/presentation/pages/create_video_page.dart';
// import 'package:xinglian/src/features/creation/presentation/pages/creation_center_page.dart'; // 暂时未使用
import 'package:xinglian/src/features/discovery/presentation/pages/listen_category_list_page.dart';
import 'package:xinglian/src/features/discovery/presentation/pages/listen_detail_page.dart';
// import 'package:xinglian/src/features/discovery/presentation/pages/listen_page.dart'; // 暂时未使用
import 'package:xinglian/src/features/home/<USER>/pages/watch_page.dart';
import 'package:xinglian/src/features/main/presentation/pages/auth_wrapper.dart';
import 'package:xinglian/src/features/main/presentation/pages/main_shell.dart';
import 'package:xinglian/src/features/messages/presentation/pages/message_center_page.dart';
import 'package:xinglian/src/features/novel/presentation/pages/novel_comments_page.dart';
import 'package:xinglian/src/features/novel/presentation/pages/novel_reader_page.dart';
import 'package:xinglian/src/features/profile/presentation/pages/character_detail_page.dart';
import 'package:xinglian/src/features/profile/presentation/pages/creator_profile_page.dart';
import 'package:xinglian/src/features/profile/presentation/pages/level_page.dart';
import 'package:xinglian/src/features/profile/presentation/pages/profile_page.dart';
import 'package:xinglian/src/features/ranking/presentation/pages/ranking_page.dart';
import 'package:xinglian/src/features/search/presentation/pages/search_page.dart';
import 'package:xinglian/src/features/friends/presentation/pages/friends_page.dart';

final _rootNavigatorKey = GlobalKey<NavigatorState>();

final appRouter = GoRouter(
  initialLocation: '/',
  navigatorKey: _rootNavigatorKey,
  routes: [
    GoRoute(
      path: '/',
      builder: (context, state) => const AuthWrapper(),
    ),
    // Main application shell
    StatefulShellRoute.indexedStack(
      builder: (context, state, navigationShell) {
        return MainShell(navigationShell: navigationShell);
      },
      branches: [
        // Branch 0: 聊天 (原'消息')
        StatefulShellBranch(
          routes: [
            GoRoute(
              path: '/chat',
              pageBuilder: (context, state) => const NoTransitionPage(child: MessageCenterPage()),
            ),
          ],
        ),
        // Branch 1: 好友 (新页面)
        StatefulShellBranch(
          routes: [
            GoRoute(
              path: '/friends',
              pageBuilder: (context, state) => const NoTransitionPage(child: FriendsPage()),
            ),
          ],
        ),
        // Branch 2: 推荐 (原'看')
        StatefulShellBranch(
          routes: [
            GoRoute(
              path: '/recommend',
              pageBuilder: (context, state) => const NoTransitionPage(child: WatchPage()),
            ),
          ],
        ),
        // Branch 3: 我的 (原'profile')
        StatefulShellBranch(
          routes: [
            GoRoute(
              path: '/profile',
              pageBuilder: (context, state) => const NoTransitionPage(child: ProfilePage()),
            ),
          ],
        ),
      ],
    ),

    // Standalone routes (no shell)
    GoRoute(
      path: '/login',
      builder: (context, state) => const LoginPage(),
    ),
    GoRoute(
      path: '/register',
      builder: (context, state) => const RegisterPage(),
    ),
    GoRoute(
      path: '/search',
      builder: (context, state) => const SearchPage(),
    ),
    GoRoute(
      path: '/ranking',
      builder: (context, state) => const RankingPage(),
    ),
    GoRoute(
      path: '/level',
      builder: (context, state) => const LevelPage(),
    ),
    GoRoute(
      path: '/creator-profile',
      builder: (context, state) {
        final extra = state.extra as Map<String, dynamic>?;
        final creatorName = extra != null ? extra['creatorName'] as String? : null;
        return CreatorProfilePage(creatorName: creatorName);
      },
    ),
    GoRoute(
      path: '/character-detail/:agentId',
      builder: (context, state) {
        final agentId = state.pathParameters['agentId']!;
        return CharacterDetailPage(agentId: agentId);
      }
    ),
    // GoRoute(
    //   path: '/ai-chat/:agentId',
    //   builder: (context, state) {
    //     final agentId = state.pathParameters['agentId']!;
    //     final message = state.uri.queryParameters['initialMessage'];
    //     return AiChatPage(agentId: agentId, initialMessage: message);
    //   }
    // ),
    GoRoute(
      path: '/create-video',
      builder: (context, state) => const CreateVideoPage(),
    ),
    GoRoute(
      path: '/create-story',
      builder: (context, state) => const CreateStoryPage(),
    ),
    GoRoute(
      path: '/create-agent',
      builder: (context, state) => const CreateAgentPage(),
    ),
    GoRoute(
      path: '/novel-reader/:bookId',
      builder: (context, state) {
        final bookId = state.pathParameters['bookId']!;
        return NovelReaderPage(bookId: bookId);
      },
    ),
    GoRoute(
      path: '/novel-comments/:bookId',
      builder: (context, state) {
         final bookId = state.pathParameters['bookId']!;
        return NovelCommentsPage(bookId: bookId);
      }
    ),
    GoRoute(
      path: '/listen-detail',
      builder: (context, state) => const ListenDetailPage(),
    ),
    GoRoute(
      path: '/listen-category-list/:title',
      builder: (context, state) {
        final title = state.pathParameters['title']!;
        return ListenCategoryListPage(title: title);
      }
    ),
    GoRoute(
      path: '/chat/:chatId',
      builder: (context, state) {
        final chatId = state.pathParameters['chatId']!;
        return ChatPlayerPage(chatId: chatId);
      },
    ),
  ],
);

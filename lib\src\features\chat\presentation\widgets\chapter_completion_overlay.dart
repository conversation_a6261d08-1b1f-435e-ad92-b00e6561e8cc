import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:xinglian/src/core/theme/app_colors.dart';
import 'package:xinglian/src/features/chat/bloc/chat_player/chat_player_bloc.dart';

/// 章节完成覆盖层组件
/// 使用磨玻璃效果，显示中性化的章节完成提示
class ChapterCompletionOverlay extends StatefulWidget {
  final ChatPlayerState state;
  
  const ChapterCompletionOverlay({
    super.key,
    required this.state,
  });

  @override
  State<ChapterCompletionOverlay> createState() => _ChapterCompletionOverlayState();
}

class _ChapterCompletionOverlayState extends State<ChapterCompletionOverlay>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 0.8, curve: Curves.elasticOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.3, 1.0, curve: Curves.easeOutCubic),
    ));

    // 启动动画
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Container(
            width: double.infinity,
            height: double.infinity,
            child: Stack(
              children: [
                // 背景模糊层
                Positioned.fill(
                  child: BackdropFilter(
                    filter: ImageFilter.blur(
                      sigmaX: 10.0 * _fadeAnimation.value,
                      sigmaY: 10.0 * _fadeAnimation.value,
                    ),
                    child: Container(
                      color: Colors.black.withOpacity(0.3 * _fadeAnimation.value),
                    ),
                  ),
                ),
                
                // 主要内容
                Center(
                  child: Transform.scale(
                    scale: _scaleAnimation.value,
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: _buildCompletionCard(context),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCompletionCard(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 32),
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(23),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(23),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 章节完结文字
                _buildCompletionText(),
                
                const SizedBox(height: 24),
                
                // 分数显示
                _buildScoreDisplay(),
                
                const SizedBox(height: 32),
                
                // 操作按钮
                _buildActionButtons(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCompletionText() {
    return Column(
      children: [
        // 主标题
        Text(
          '本章完结',
          style: TextStyle(
            color: Colors.white,
            fontSize: 28,
            fontWeight: FontWeight.w300,
            letterSpacing: 2.0,
            shadows: [
              Shadow(
                blurRadius: 8.0,
                color: Colors.black.withOpacity(0.5),
                offset: const Offset(0, 2),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 8),
        
        // 副标题
        Text(
          '感谢您的参与',
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 16,
            fontWeight: FontWeight.w400,
            letterSpacing: 1.0,
          ),
        ),
      ],
    );
  }

  Widget _buildScoreDisplay() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(19),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(19),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.star_rounded,
                  color: AppColors.accentYellow,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  '本章得分：${widget.state.currentProgress}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        // 继续本章按钮
        Expanded(
          child: _buildGlassButton(
            text: '继续本章',
            onPressed: () => _onContinueChapter(context),
            isPrimary: false,
          ),
        ),
        
        const SizedBox(width: 16),
        
        // 下一章节按钮
        Expanded(
          child: _buildGlassButton(
            text: '下一章节',
            onPressed: () => _onNextChapter(context),
            isPrimary: true,
          ),
        ),
      ],
    );
  }

  Widget _buildGlassButton({
    required String text,
    required VoidCallback onPressed,
    required bool isPrimary,
  }) {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isPrimary 
            ? AppColors.accentYellow.withOpacity(0.5)
            : Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(15),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Material(
            color: isPrimary 
              ? AppColors.accentYellow.withOpacity(0.2)
              : Colors.white.withOpacity(0.1),
            child: InkWell(
              onTap: onPressed,
              borderRadius: BorderRadius.circular(15),
              child: Center(
                child: Text(
                  text,
                  style: TextStyle(
                    color: isPrimary ? AppColors.accentYellow : Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.5,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _onContinueChapter(BuildContext context) {
    // 关闭覆盖层并继续当前章节
    context.read<ChatPlayerBloc>().add(ContinueCurrentChapter());
  }

  void _onNextChapter(BuildContext context) {
    // 派发LoadNextChapter事件来加载下一章节
    context.read<ChatPlayerBloc>().add(LoadNextChapter());
  }
}
